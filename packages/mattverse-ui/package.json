{"name": "@mattverse/mattverse-ui", "version": "1.1.0", "private": true, "description": "Shared UI components for Mattverse applications", "type": "module", "main": "./dist/index.cjs.js", "module": "./dist/index.es.js", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.es.js", "require": "./dist/index.cjs.js", "types": "./dist/index.d.ts"}, "./style.css": "./dist/style.css"}, "files": ["dist"], "scripts": {"build": "npm run build:styles && vite build", "build:styles": "tailwindcss -i src/styles/index.css -o dist/style.css --watch=false", "build:types": "vue-tsc --project tsconfig.build.json", "dev": "vite build --watch", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint src --ext .ts,.tsx,.vue", "lint:fix": "eslint src --ext .ts,.tsx,.vue --fix", "typecheck": "vue-tsc --noEmit"}, "dependencies": {"@mattverse/shared": "workspace:*", "@tailwindcss/cli": "^4.1.11", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/vite": "^4.1.11", "@tanstack/vue-table": "^8.21.3", "@vee-validate/zod": "^4.15.1", "@vueuse/core": "^11.3.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "embla-carousel-vue": "^8.6.0", "lightningcss": "^1.30.1", "lucide-vue-next": "^0.447.0", "postcss": "^8.4.0", "radix-vue": "^1.9.0", "reka-ui": "^2.4.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.3.6", "vaul-vue": "^0.4.1", "vee-validate": "^4.13.0", "vue": "^3.5.0", "vue-sonner": "^1.3.2", "zod": "^3.23.0"}, "devDependencies": {"@mattverse/configs": "workspace:*", "@vitejs/plugin-vue": "^5.0.0", "autoprefixer": "^10.4.0", "shadcn-vue": "^2.2.0", "tailwindcss": "^4.1.11", "vite": "^5.4.0", "vue-tsc": "^2.0.0"}, "peerDependencies": {"vue": "^3.5.0"}}