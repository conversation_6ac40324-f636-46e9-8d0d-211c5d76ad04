const tailwindcss = require('@tailwindcss/postcss')
const lightningcss = require('lightningcss')
const tailwindConfig = require('./tailwind.config.js')

module.exports = {
  plugins: [
    tailwindcss(),
    process.env.NODE_ENV === 'production'
      ? lightningcss({
          // Configure Lightning CSS with the content paths from your Tailwind config
          // This enables unused CSS purging
          unusedSymbols: tailwindConfig.content,
        })
      : null,
  ],
}
