import tailwindcss from '@tailwindcss/postcss'
import lightningcss from 'lightningcss'
import tailwindConfig from './tailwind.config.ts'

export default {
  plugins: [
    tailwindcss(),
    process.env.NODE_ENV === 'production'
      ? lightningcss({
          // Configure Lightning CSS with the content paths from your Tailwind config
          // This enables unused CSS purging
          unusedSymbols: tailwindConfig.content,
        })
      : null,
  ],
}
